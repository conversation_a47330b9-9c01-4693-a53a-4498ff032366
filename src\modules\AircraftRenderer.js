import aircraftImage from '@/assets/aircraft.png';

/**
 * 飞机渲染器类
 */
export class AircraftRenderer {
  constructor(ctx) {
    this.ctx = ctx;
    this.image = new Image();
    this.image.src = aircraftImage;
    this.imageLoaded = false;
    this.image.onload = () => {
      this.imageLoaded = true;
      console.log("飞机图像加载完成");
    };
    this.image.onerror = (err) => {
      console.error("飞机图像加载失败:", err);
    };
    
    // 飞机图像默认尺寸
    this.width = 30;
    this.height = 30;
    
    // 根据飞机类型和重量级别的颜色
    this.colors = {
      'departure': '#3498db', // 蓝色用于起飞
      'arrival': '#e74c3c',   // 红色用于降落
      '1': '#1abc9c',  // 轻型飞机
      '2': '#f39c12',  // 中型飞机
      '3': '#8e44ad'   // 重型飞机
    };
  }

  /**
   * 渲染单个飞机
   * @param {Object} aircraftInfo - 飞机信息和位置
   */
  renderAircraft(aircraftInfo) {
    if (!aircraftInfo || !aircraftInfo.position) return;
    
    const position = aircraftInfo.position;
    const type = aircraftInfo.type;
    const weightClass = aircraftInfo.weightClass;
    
    this.ctx.save();
    
    // 移动到飞机位置
    this.ctx.translate(position.x, position.y);
    
    // 旋转到飞机方向
    this.ctx.rotate(position.angle);
    
    if (this.imageLoaded) {
      // 使用图像绘制飞机
      this.ctx.drawImage(
        this.image, 
        -this.width / 2,  // 图像左上角x坐标 
        -this.height / 2, // 图像左上角y坐标
        this.width,       // 图像宽度
        this.height       // 图像高度
      );
      
      // 添加颜色指示器
      this.ctx.beginPath();
      this.ctx.arc(0, 0, 4, 0, Math.PI * 2);
      this.ctx.fillStyle = this.colors[type] || this.colors[weightClass] || '#333333';
      this.ctx.fill();
      this.ctx.strokeStyle = '#ffffff';
      this.ctx.lineWidth = 1;
      this.ctx.stroke();
    } else {
      // 图像未加载完成时绘制一个占位符
      this.drawPlaceholder(type, weightClass);
    }
    
    this.ctx.restore();
  }
  
  /**
   * 渲染多架飞机
   * @param {Array} aircrafts - 飞机列表
   */
  render(aircrafts) {
    if (!aircrafts || !Array.isArray(aircrafts)) return;
    
    aircrafts.forEach(aircraft => {
      this.renderAircraft(aircraft);
    });
  }
  
  /**
   * 绘制占位符（当图像未加载完成时）
   * @param {String} type - 飞机类型
   * @param {String} weightClass - 飞机重量级别
   */
  drawPlaceholder(type, weightClass) {
    // 绘制一个简单的三角形作为占位符
    this.ctx.beginPath();
    this.ctx.moveTo(15, 0);
    this.ctx.lineTo(-10, -10);
    this.ctx.lineTo(-5, 0);
    this.ctx.lineTo(-10, 10);
    this.ctx.closePath();
    
    this.ctx.fillStyle = this.colors[type] || this.colors[weightClass] || '#3498db';
    this.ctx.fill();
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();
  }
} 